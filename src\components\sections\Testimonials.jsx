import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi'

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    title: "Professional Bodybuilder",
    quote: "MuscleMax supplements have been a game changer for my competition prep. The protein quality is unmatched, and the results speak for themselves.",
    imageBefore: "https://images.pexels.com/photos/3757952/pexels-photo-3757952.jpeg?auto=compress&cs=tinysrgb&w=1600",
    imageAfter: "https://images.pexels.com/photos/1438081/pexels-photo-1438081.jpeg?auto=compress&cs=tinysrgb&w=1600",
    productUsed: "Performance Whey Isolate & Mass Gainer 5000",
    duration: "12 weeks transformation",
  },
  {
    id: 2,
    name: "<PERSON>",
    title: "Fitness Coach",
    quote: "I recommend MuscleMax products to all my clients. Their Pre-Workout gives amazing energy without the crash, and the BCAAs aid in faster recovery between sessions.",
    imageBefore: "https://images.pexels.com/photos/6551174/pexels-photo-6551174.jpeg?auto=compress&cs=tinysrgb&w=1600",
    imageAfter: "https://images.pexels.com/photos/1085744/pexels-photo-1085744.jpeg?auto=compress&cs=tinysrgb&w=1600",
    productUsed: "Explosive Pre-Workout & Essential BCAAs",
    duration: "8 weeks transformation",
  },
  {
    id: 3,
    name: "David Williams",
    title: "Powerlifter",
    quote: "Since using MuscleMax Creatine, I've added 50lbs to my deadlift and 40lbs to my bench press. Their protein also tastes amazing without any chalky aftertaste.",
    imageBefore: "https://images.pexels.com/photos/4172487/pexels-photo-4172487.jpeg?auto=compress&cs=tinysrgb&w=1600",
    imageAfter: "https://images.pexels.com/photos/7674494/pexels-photo-7674494.jpeg?auto=compress&cs=tinysrgb&w=1600",
    productUsed: "Advanced Creatine Monohydrate",
    duration: "16 weeks transformation",
  }
]

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState(null)
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  })
  
  const currentTestimonial = testimonials[currentIndex]
  
  // Auto-rotate testimonials
  useEffect(() => {
    const timer = setInterval(() => {
      goToNext()
    }, 8000)
    
    return () => clearInterval(timer)
  }, [currentIndex])
  
  const goToPrev = () => {
    setDirection('left')
    setCurrentIndex(prevIndex => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
  }
  
  const goToNext = () => {
    setDirection('right')
    setCurrentIndex(prevIndex => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
  }

  const variants = {
    enter: (direction) => ({
      x: direction === 'right' ? 300 : -300,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction === 'right' ? -300 : 300,
      opacity: 0,
    }),
  }

  return (
    <section id="testimonials" className="py-20 bg-gradient-to-b from-primary-50 to-white relative">
      <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-white to-transparent"></div>
      <div className="container-custom" ref={ref}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="section-title text-secondary-900">
            Transformation <span className="text-primary-500">Stories</span>
          </h2>
          <p className="section-subtitle">
            Real results from real customers who trusted MuscleMax for their fitness journey
          </p>
        </motion.div>
        
        <div className="relative max-w-5xl mx-auto">
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ type: 'tween', duration: 0.5 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center"
            >
              {/* Before/After Images */}
              <div className="rounded-xl overflow-hidden shadow-lg relative">
                <div className="relative h-[400px] md:h-[500px]">
                  {/* Before Image (Left Half) */}
                  <div className="absolute top-0 left-0 w-1/2 h-full overflow-hidden border-r border-white">
                    <img 
                      src={currentTestimonial.imageBefore} 
                      alt={`${currentTestimonial.name} before`} 
                      className="h-full w-[200%] object-cover object-center"
                    />
                    <div className="absolute bottom-4 left-4 bg-secondary-900/80 text-white text-xs font-bold px-2 py-1 rounded">
                      BEFORE
                    </div>
                  </div>
                  
                  {/* After Image (Right Half) */}
                  <div className="absolute top-0 right-0 w-1/2 h-full overflow-hidden">
                    <img 
                      src={currentTestimonial.imageAfter} 
                      alt={`${currentTestimonial.name} after`} 
                      className="h-full w-[200%] object-cover object-center -ml-[100%]"
                    />
                    <div className="absolute bottom-4 right-4 bg-primary-500/90 text-white text-xs font-bold px-2 py-1 rounded">
                      AFTER
                    </div>
                  </div>
                  
                  {/* Divider Line */}
                  <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-white"></div>
                </div>
              </div>
              
              {/* Testimonial Content */}
              <div className="text-center lg:text-left p-6">
                <svg className="w-12 h-12 text-primary-300/30 mb-6 mx-auto lg:mx-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
                
                <blockquote className="text-xl md:text-2xl font-medium text-secondary-800 mb-8">
                  "{currentTestimonial.quote}"
                </blockquote>
                
                <div className="mb-8">
                  <h4 className="text-lg font-semibold text-secondary-900">{currentTestimonial.name}</h4>
                  <p className="text-primary-500">{currentTestimonial.title}</p>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg text-sm text-secondary-700">
                  <p>
                    <span className="font-semibold">Products used:</span> {currentTestimonial.productUsed}
                  </p>
                  <p>
                    <span className="font-semibold">Duration:</span> {currentTestimonial.duration}
                  </p>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
          
          {/* Navigation Controls */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, i) => (
              <button
                key={i}
                onClick={() => {
                  setDirection(i > currentIndex ? 'right' : 'left')
                  setCurrentIndex(i)
                }}
                className={`w-3 h-3 rounded-full transition-colors ${
                  currentIndex === i ? 'bg-primary-500' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${i + 1}`}
              />
            ))}
          </div>
          
          <button
            onClick={goToPrev}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 lg:-translate-x-12 bg-white/80 hover:bg-white rounded-full p-3 shadow-md z-10"
            aria-label="Previous testimonial"
          >
            <FiChevronLeft className="text-secondary-900 text-xl" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 lg:translate-x-12 bg-white/80 hover:bg-white rounded-full p-3 shadow-md z-10"
            aria-label="Next testimonial"
          >
            <FiChevronRight className="text-secondary-900 text-xl" />
          </button>
        </div>
      </div>
    </section>
  )
}

export default Testimonials