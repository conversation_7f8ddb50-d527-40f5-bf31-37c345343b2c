/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0faf0',
          100: '#dbf5db',
          200: '#b8eab8',
          300: '#8fdd89', // Main primary color
          400: '#68c95f',
          500: '#4aae3e',
          600: '#3a8d31',
          700: '#2f7029',
          800: '#285824',
          900: '#1f391c',
          950: '#0f1e0e',
        },
        secondary: {
          50: '#f4f9f4',
          100: '#e6f5e6',
          200: '#c6e9c6',
          300: '#92d392',
          400: '#60b760',
          500: '#429b42',
          600: '#327c32',
          700: '#2a632a',
          800: '#254f25',
          900: '#1A2421', // Dark accent
          950: '#0c130c',
        },
        accent: {
          300: '#ffcc80',
          400: '#ffb74d',
          500: '#ffa726', // Orange accent
        },
      },
      fontFamily: {
        sans: ['Inter var', 'Inter', 'system-ui', 'sans-serif'],
        display: ['Orbitron', 'system-ui', 'sans-serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'hero-pattern': "url('https://images.pexels.com/photos/4164761/pexels-photo-4164761.jpeg?auto=compress&cs=tinysrgb&w=1600')",
      },
      boxShadow: {
        'neon': '0 0 5px theme(colors.primary.300), 0 0 20px theme(colors.primary.300)',
        'neon-lg': '0 0 10px theme(colors.primary.300), 0 0 30px theme(colors.primary.300)',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.07)',
      },
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [],
}