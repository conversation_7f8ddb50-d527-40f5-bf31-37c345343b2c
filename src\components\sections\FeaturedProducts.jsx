import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import ProductCard from '../ui/ProductCard'

const productCategories = ['All', 'Protein', 'Pre-Workout', 'Weight Gainers', 'BCAAs']

const products = [
  {
    id: 1,
    name: "Performance Whey Isolate",
    description: "Ultra-pure whey protein isolate with 27g protein per serving. Fast-absorbing formula.",
    price: 59.99,
    originalPrice: 69.99,
    category: "Protein",
    rating: 4.9,
    reviewCount: 127,
    image: "https://images.pexels.com/photos/3910071/pexels-photo-3910071.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
  {
    id: 2,
    name: "Explosive Pre-Workout",
    description: "Maximum energy formula with caffeine, creatine, and beta-alanine for intense workouts.",
    price: 49.99,
    originalPrice: 59.99,
    category: "Pre-Workout",
    rating: 4.8,
    reviewCount: 94,
    image: "https://images.pexels.com/photos/5946081/pexels-photo-5946081.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
  {
    id: 3,
    name: "Mass Gainer 5000",
    description: "High-calorie formula with 1250 calories per serving. Perfect for bulking phases.",
    price: 64.99,
    originalPrice: 74.99,
    category: "Weight Gainers",
    rating: 4.7,
    reviewCount: 86,
    image: "https://images.pexels.com/photos/3735141/pexels-photo-3735141.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
  {
    id: 4,
    name: "Essential BCAAs",
    description: "2:1:1 ratio of leucine, isoleucine, and valine. Supports muscle recovery and growth.",
    price: 39.99,
    originalPrice: 44.99,
    category: "BCAAs",
    rating: 4.8,
    reviewCount: 103,
    image: "https://images.pexels.com/photos/53068/protein-muscle-building-supplement-health-53068.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
  {
    id: 5,
    name: "Advanced Creatine Monohydrate",
    description: "Micronized creatine for enhanced absorption and strength gains. 100 servings.",
    price: 29.99,
    originalPrice: 34.99,
    category: "Pre-Workout",
    rating: 4.9,
    reviewCount: 152,
    image: "https://images.pexels.com/photos/4397840/pexels-photo-4397840.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
  {
    id: 6,
    name: "Plant-Based Protein",
    description: "Complete plant protein from pea, rice, and hemp sources. 25g protein per serving.",
    price: 54.99,
    originalPrice: 64.99,
    category: "Protein",
    rating: 4.7,
    reviewCount: 78,
    image: "https://images.pexels.com/photos/139390/pexels-photo-139390.jpeg?auto=compress&cs=tinysrgb&w=1600"
  },
]

const FeaturedProducts = ({ addToCart }) => {
  const [activeCategory, setActiveCategory] = useState('All')
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  })

  const filteredProducts = activeCategory === 'All' 
    ? products 
    : products.filter(product => product.category === activeCategory)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <section id="products" className="py-20 bg-white">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center"
          ref={ref}
        >
          <h2 className="section-title bg-clip-text text-transparent bg-gradient-to-r from-primary-500 to-primary-300">
            Featured Products
          </h2>
          <p className="section-subtitle">
            Premium quality supplements designed to maximize your gains and enhance your performance
          </p>
        </motion.div>

        <div className="mb-10 flex flex-wrap justify-center gap-4">
          {productCategories.map((category) => (
            <motion.button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                activeCategory === category 
                  ? 'bg-primary-400 text-white shadow-md' 
                  : 'bg-gray-100 text-secondary-700 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {filteredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              addToCart={() => addToCart(product)}
            />
          ))}
        </motion.div>
        
        <div className="text-center mt-12">
          <motion.a
            href="#"
            className="btn btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Products
          </motion.a>
        </div>
      </div>
    </section>
  )
}

export default FeaturedProducts