import { motion } from 'framer-motion'

const Hero = () => {
  return (
    <section className="relative h-screen min-h-[700px] flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-secondary-900/90 to-secondary-900/70 z-10"></div>
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          poster="https://images.pexels.com/photos/1229356/pexels-photo-1229356.jpeg?auto=compress&cs=tinysrgb&w=1600"
        >
          <source src="https://player.vimeo.com/external/539248044.sd.mp4?s=dcc6a15db0ec832e5f59049637155189ce8b96fa&profile_id=164" type="video/mp4" />
        </video>
      </div>
      
      <div className="container-custom relative z-20 grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Hero Text Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold text-white leading-tight mb-6">
            Transform Your <span className="text-primary-300">Body</span> <br />
            Elevate Your <span className="text-primary-300">Performance</span>
          </h1>
          
          <p className="text-gray-200 text-lg md:text-xl mb-8 max-w-lg">
            Premium-quality supplements scientifically formulated to maximize your results. 
            Take your fitness journey to the next level.
          </p>
          
          <div className="flex flex-wrap gap-4">
            <motion.a
              href="#products"
              className="btn btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Shop Products
            </motion.a>
            
            <motion.a
              href="#benefits"
              className="btn btn-outline border-white text-white hover:bg-white/10"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Learn More
            </motion.a>
          </div>
          
          <div className="mt-12 flex items-center space-x-6">
            <div className="flex -space-x-2">
              {[1, 2, 3, 4].map((i) => (
                <img 
                  key={i}
                  src={`https://i.pravatar.cc/150?img=${i+20}`}
                  alt={`User ${i}`}
                  className="w-10 h-10 rounded-full border-2 border-white"
                />
              ))}
            </div>
            <div className="text-white">
              <strong className="block text-primary-300 text-lg">2,000+</strong>
              <span className="text-sm">Satisfied customers</span>
            </div>
            
            <div className="pl-4 border-l border-white/30 text-white">
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map(i => (
                  <svg key={i} className="w-4 h-4 text-primary-300 fill-current" viewBox="0 0 24 24">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
              <span className="text-sm">5.0 average rating</span>
            </div>
          </div>
        </motion.div>
        
        {/* Product Image */}
        <motion.div
          className="hidden lg:block"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <div className="relative h-[500px]">
            <motion.img
              src="https://images.pexels.com/photos/8159658/pexels-photo-8159658.jpeg?auto=compress&cs=tinysrgb&w=1600"
              alt="Premium Protein Powder"
              className="absolute top-0 right-0 h-[500px] object-cover rounded-2xl"
              initial={{ y: 0 }}
              animate={{ y: [0, -20, 0] }}
              transition={{ 
                repeat: Infinity, 
                duration: 6,
                ease: "easeInOut" 
              }}
            />
            
            {/* Floating Product Details */}
            <motion.div 
              className="absolute -bottom-4 -left-8 glass rounded-xl p-4 max-w-[220px]"
              initial={{ y: 0 }}
              animate={{ y: [0, 15, 0] }}
              transition={{ 
                repeat: Infinity, 
                duration: 5,
                ease: "easeInOut",
                delay: 0.5
              }}
            >
              <p className="text-white text-sm font-medium">Premium Whey Protein</p>
              <div className="flex justify-between items-center mt-2">
                <span className="text-primary-300 font-bold">$59.99</span>
                <span className="text-white text-xs bg-primary-500 px-2 py-1 rounded">Best Seller</span>
              </div>
            </motion.div>
            
            {/* Floating Badge */}
            <motion.div
              className="absolute top-6 -left-10 bg-primary-300 text-secondary-900 text-sm font-bold px-4 py-2 rounded-full"
              initial={{ rotate: -5 }}
              animate={{ rotate: [0, 5, 0] }}
              transition={{ 
                repeat: Infinity, 
                duration: 4,
                ease: "easeInOut" 
              }}
            >
              30% OFF TODAY
            </motion.div>
          </div>
        </motion.div>
      </div>
      
      {/* Scroll Indicator */}
      <motion.div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5, duration: 0.5 }}
      >
        <motion.div 
          className="w-8 h-12 rounded-full border-2 border-white flex items-center justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ 
            repeat: Infinity, 
            duration: 1.5,
            ease: "easeInOut" 
          }}
        >
          <div className="w-1 h-3 bg-white rounded-full"></div>
        </motion.div>
        <p className="text-white text-xs mt-2 font-medium text-center">Scroll</p>
      </motion.div>
    </section>
  )
}

export default Hero