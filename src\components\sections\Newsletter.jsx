import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const Newsletter = () => {
  const [email, setEmail] = useState('')
  const [submitted, setSubmitted] = useState(false)
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    // Here you would typically send the email to your API
    if (email) {
      setSubmitted(true)
      // Reset form after successful submission
      setTimeout(() => {
        setEmail('')
        setSubmitted(false)
      }, 5000)
    }
  }

  return (
    <section id="contact" className="py-20 bg-secondary-900 relative">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-white to-transparent opacity-5"></div>
      <div className="absolute -top-10 left-0 w-full overflow-hidden">
        <svg 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none" 
          className="w-full h-20 text-white opacity-5"
        >
          <path 
            d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" 
            fill="currentColor"
          ></path>
        </svg>
      </div>

      <div className="container-custom relative z-10" ref={ref}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="max-w-3xl mx-auto text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Join the <span className="text-primary-300">MuscleMax</span> Community
          </h2>
          <p className="text-gray-300 mb-8">
            Subscribe to our newsletter and get 15% off your first order, plus exclusive access to workout tips, nutrition advice, and special offers.
          </p>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 md:p-8 border border-white/5">
            {!submitted ? (
              <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="flex-grow px-4 py-3 rounded-lg bg-white/80 border border-white/10 focus:outline-none focus:ring-2 focus:ring-primary-300 text-secondary-900"
                  required
                />
                <motion.button
                  type="submit"
                  className="btn btn-primary whitespace-nowrap"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Get 15% Off
                </motion.button>
              </form>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-primary-500/20 text-primary-300 p-4 rounded-lg"
              >
                <p className="font-medium">Thank you for subscribing!</p>
                <p className="text-sm mt-1">Your discount code will be sent to your email shortly.</p>
              </motion.div>
            )}
            
            <p className="text-gray-400 text-xs mt-4">
              By subscribing, you agree to our Privacy Policy and consent to receive marketing emails.
              We respect your privacy and you can unsubscribe at any time.
            </p>
          </div>
          
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/5 p-6 rounded-lg">
              <div className="text-primary-300 text-2xl mb-3">📍</div>
              <h3 className="text-white font-semibold text-lg mb-1">Visit Our Store</h3>
              <p className="text-gray-300 text-sm">
                123 Fitness Street<br />
                Muscle City, MC 10001
              </p>
            </div>
            
            <div className="bg-white/5 p-6 rounded-lg">
              <div className="text-primary-300 text-2xl mb-3">📱</div>
              <h3 className="text-white font-semibold text-lg mb-1">Call Us</h3>
              <p className="text-gray-300 text-sm">
                +1 (555) 123-4567<br />
                Mon-Fri: 9AM-5PM
              </p>
            </div>
            
            <div className="bg-white/5 p-6 rounded-lg">
              <div className="text-primary-300 text-2xl mb-3">📧</div>
              <h3 className="text-white font-semibold text-lg mb-1">Email Us</h3>
              <p className="text-gray-300 text-sm">
                <EMAIL><br />
                <EMAIL>
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Newsletter