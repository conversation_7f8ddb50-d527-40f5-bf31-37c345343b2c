import { motion, AnimatePresence } from 'framer-motion'
import { FiX, FiShoppingBag, FiPlus, FiMinus, FiTrash2 } from 'react-icons/fi'

const CartSidebar = ({ isOpen, toggleCart, cartItems, removeItem, updateQuantity }) => {
  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }
  
  const calculateShipping = () => {
    const subtotal = calculateSubtotal()
    return subtotal > 100 ? 0 : 9.99
  }
  
  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping()
  }
  
  const isCartEmpty = cartItems.length === 0

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-secondary-900/70 z-40"
            onClick={toggleCart}
          />
          
          {/* <PERSON>t Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="fixed top-0 right-0 w-full max-w-md h-full bg-white z-50 shadow-xl flex flex-col"
          >
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-xl font-semibold text-secondary-900">Your Cart</h2>
              <button
                onClick={toggleCart}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close cart"
              >
                <FiX className="text-xl" />
              </button>
            </div>
            
            {isCartEmpty ? (
              <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
                <FiShoppingBag className="text-5xl text-gray-300 mb-4" />
                <h3 className="text-xl font-medium text-secondary-900 mb-2">Your cart is empty</h3>
                <p className="text-gray-500 mb-6">Looks like you haven't added any products to your cart yet.</p>
                <button 
                  onClick={toggleCart}
                  className="btn btn-primary"
                >
                  Continue Shopping
                </button>
              </div>
            ) : (
              <>
                <div className="flex-1 overflow-y-auto p-4">
                  <ul className="space-y-4">
                    {cartItems.map(item => (
                      <li key={item.id} className="flex gap-4 p-3 rounded-lg hover:bg-gray-50">
                        <img 
                          src={item.image} 
                          alt={item.name} 
                          className="w-20 h-20 object-cover rounded-md"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-secondary-900">{item.name}</h4>
                          <p className="text-sm text-gray-500 mb-2">${item.price.toFixed(2)}</p>
                          <div className="flex items-center">
                            <button 
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="p-1 rounded-full hover:bg-gray-200"
                              aria-label="Decrease quantity"
                            >
                              <FiMinus className="text-sm" />
                            </button>
                            <span className="mx-2 w-8 text-center">{item.quantity}</span>
                            <button 
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="p-1 rounded-full hover:bg-gray-200"
                              aria-label="Increase quantity"
                            >
                              <FiPlus className="text-sm" />
                            </button>
                          </div>
                        </div>
                        <div className="flex flex-col items-end justify-between">
                          <span className="font-medium text-secondary-900">
                            ${(item.price * item.quantity).toFixed(2)}
                          </span>
                          <button 
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 p-1 hover:bg-red-50 rounded-full"
                            aria-label="Remove item"
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="border-t p-4 bg-gray-50">
                  <div className="space-y-3 mb-4">
                    <div className="flex justify-between text-secondary-900">
                      <span>Subtotal</span>
                      <span>${calculateSubtotal().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-secondary-900">
                      <span>Shipping</span>
                      <span>{calculateShipping() === 0 ? 'Free' : `$${calculateShipping().toFixed(2)}`}</span>
                    </div>
                    <div className="flex justify-between font-semibold text-lg text-secondary-900 pt-3 border-t">
                      <span>Total</span>
                      <span>${calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                  
                  <button className="btn btn-primary w-full mb-3">
                    Checkout
                  </button>
                  <button 
                    onClick={toggleCart}
                    className="btn btn-outline w-full"
                  >
                    Continue Shopping
                  </button>
                </div>
              </>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

export default CartSidebar