import { useState, useEffect } from 'react'
import { FiShoppingCart, FiMenu, FiX } from 'react-icons/fi'
import { motion, AnimatePresence } from 'framer-motion'

const Header = ({ toggleCart, cartItemCount }) => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      if (scrollPosition > 50) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header 
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white/95 shadow-md backdrop-blur-sm py-3' : 'bg-transparent py-5'
      }`}
    >
      <div className="container-custom mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <a href="/" className="flex items-center">
            <motion.div 
              initial={{ rotate: -10 }}
              animate={{ rotate: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-3xl mr-1">💪</span>
            </motion.div>
            <span className={`font-display text-xl md:text-2xl font-bold ${
              isScrolled ? 'text-secondary-900' : 'text-white'
            }`}>
              MUSCLE<span className="text-primary-400">MAX</span>
            </span>
          </a>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a 
            href="#products" 
            className={`font-medium transition-colors ${
              isScrolled ? 'text-secondary-900 hover:text-primary-500' : 'text-white hover:text-primary-300'
            }`}
          >
            Products
          </a>
          <a 
            href="#benefits" 
            className={`font-medium transition-colors ${
              isScrolled ? 'text-secondary-900 hover:text-primary-500' : 'text-white hover:text-primary-300'
            }`}
          >
            Benefits
          </a>
          <a 
            href="#testimonials" 
            className={`font-medium transition-colors ${
              isScrolled ? 'text-secondary-900 hover:text-primary-500' : 'text-white hover:text-primary-300'
            }`}
          >
            Testimonials
          </a>
          <a 
            href="#contact" 
            className={`font-medium transition-colors ${
              isScrolled ? 'text-secondary-900 hover:text-primary-500' : 'text-white hover:text-primary-300'
            }`}
          >
            Contact
          </a>
        </nav>

        <div className="flex items-center">
          <motion.button
            className={`relative p-2 mr-2 rounded-full ${
              isScrolled ? 'text-secondary-900 hover:bg-gray-100' : 'text-white hover:bg-white/10'
            }`}
            whileTap={{ scale: 0.9 }}
            onClick={toggleCart}
            aria-label="Shopping Cart"
          >
            <FiShoppingCart className="text-xl" />
            {cartItemCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary-400 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                {cartItemCount}
              </span>
            )}
          </motion.button>

          {/* Mobile Menu Button */}
          <button
            className="p-2 rounded-full md:hidden"
            onClick={toggleMobileMenu}
            aria-label={isMobileMenuOpen ? "Close Menu" : "Open Menu"}
          >
            {isMobileMenuOpen ? (
              <FiX className={`text-2xl ${isScrolled ? 'text-secondary-900' : 'text-white'}`} />
            ) : (
              <FiMenu className={`text-2xl ${isScrolled ? 'text-secondary-900' : 'text-white'}`} />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div 
            className="fixed inset-0 bg-white z-40 pt-20"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.3 }}
          >
            <div className="container mx-auto px-4 py-4 flex flex-col space-y-6">
              <a 
                href="#products" 
                className="text-secondary-900 font-medium text-lg py-3 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Products
              </a>
              <a 
                href="#benefits" 
                className="text-secondary-900 font-medium text-lg py-3 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Benefits
              </a>
              <a 
                href="#testimonials" 
                className="text-secondary-900 font-medium text-lg py-3 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Testimonials
              </a>
              <a 
                href="#contact" 
                className="text-secondary-900 font-medium text-lg py-3"
                onClick={toggleMobileMenu}
              >
                Contact
              </a>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}

export default Header