@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #8fdd89;
  --color-secondary: #1A2421;
  --color-accent: #ffa726;
}

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-secondary-50 text-secondary-900 font-sans;
}

.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-glass;
}

.glass-card {
  @apply bg-white/90 backdrop-blur-sm border border-primary-100 rounded-xl shadow-glass transition-all duration-300;
}

.glass-card:hover {
  @apply bg-white/95 shadow-lg border-primary-200 scale-[1.02];
}

@layer components {
  .btn {
    @apply px-6 py-3 rounded-full font-semibold transition-all duration-300 inline-block text-center;
  }
  
  .btn-primary {
    @apply bg-primary-400 text-white hover:bg-primary-500 shadow-md hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-secondary-900 text-white hover:bg-secondary-800 shadow-md hover:shadow-lg;
  }
  
  .btn-outline {
    @apply border-2 border-primary-400 text-primary-500 hover:bg-primary-50;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-title {
    @apply text-4xl md:text-5xl font-display font-semibold mb-6;
  }
  
  .section-subtitle {
    @apply text-lg md:text-xl text-secondary-700 max-w-3xl mx-auto mb-12;
  }
}

.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.8s ease-in forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}