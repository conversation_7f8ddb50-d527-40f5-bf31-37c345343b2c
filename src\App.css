/* Overrides and additions to Tailwind styles */
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  text-align: left;
  overflow-x: hidden;
}

.app-container {
  transition: opacity 0.5s ease-in-out;
}

/* Product card glow effects */
.product-card:hover .glow-effect {
  box-shadow: 0 0 15px rgba(143, 221, 137, 0.5);
}

/* Parallax section */
.parallax-section {
  position: relative;
  overflow: hidden;
}

.parallax-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(26, 36, 33, 0.7), rgba(26, 36, 33, 0.7));
  z-index: 1;
}

.parallax-content {
  position: relative;
  z-index: 2;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #68c95f;
}

/* Cart sidebar animation */
.cart-enter {
  transform: translateX(100%);
}

.cart-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-out;
}

.cart-exit {
  transform: translateX(0);
}

.cart-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(90deg, #68c95f 0%, #8fdd89 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}