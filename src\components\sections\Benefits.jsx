import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { FiCheck, FiAward, FiTrendingUp, FiShield } from 'react-icons/fi'

const benefits = [
  {
    id: 1,
    title: "Science-backed Formulas",
    description: "Our supplements are developed by leading nutritionists and backed by scientific research for maximum effectiveness.",
    icon: FiA<PERSON>,
    color: "bg-blue-500",
  },
  {
    id: 2,
    title: "Premium Ingredients",
    description: "We use only the highest quality, pure ingredients with no fillers or artificial additives.",
    icon: FiCheck,
    color: "bg-green-500",
  },
  {
    id: 3,
    title: "Enhanced Performance",
    description: "See measurable improvements in strength, endurance, and recovery time.",
    icon: FiTrendingUp,
    color: "bg-purple-500",
  },
  {
    id: 4,
    title: "Lab Tested & Certified",
    description: "Every batch is tested for purity and potency in independent laboratories.",
    icon: FiShield,
    color: "bg-orange-500",
  },
]

const Benefits = () => {
  const [ref, inView] = useInView({
    triggerOnce: false,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.5 }
    }
  }

  return (
    <section id="benefits" className="py-20 bg-gradient-to-b from-white to-primary-50">
      <div className="container-custom" ref={ref}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="section-title text-secondary-900">
            Why Choose <span className="text-primary-500">MuscleMax</span>?
          </h2>
          <p className="section-subtitle">
            Our products are designed to help you reach your fitness goals faster with premium quality ingredients
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12"
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {benefits.map((benefit) => (
            <motion.div
              key={benefit.id}
              className="flex gap-6 items-start bg-white p-6 md:p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow"
              variants={itemVariants}
            >
              <div className={`${benefit.color} w-12 h-12 rounded-full flex items-center justify-center shrink-0 text-white`}>
                <benefit.icon className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">{benefit.title}</h3>
                <p className="text-secondary-700">{benefit.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        {/* Stats Section */}
        <motion.div 
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-6 text-center"
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
        >
          <div>
            <motion.span 
              className="block text-4xl lg:text-5xl font-bold text-primary-500 mb-2"
              initial={{ scale: 0.5 }}
              animate={inView ? { scale: 1 } : { scale: 0.5 }}
              transition={{ delay: 0.5, duration: 0.5, type: "spring" }}
            >
              50+
            </motion.span>
            <span className="text-secondary-700">Products</span>
          </div>
          
          <div>
            <motion.span 
              className="block text-4xl lg:text-5xl font-bold text-primary-500 mb-2"
              initial={{ scale: 0.5 }}
              animate={inView ? { scale: 1 } : { scale: 0.5 }}
              transition={{ delay: 0.6, duration: 0.5, type: "spring" }}
            >
              10k+
            </motion.span>
            <span className="text-secondary-700">Happy Customers</span>
          </div>
          
          <div>
            <motion.span 
              className="block text-4xl lg:text-5xl font-bold text-primary-500 mb-2"
              initial={{ scale: 0.5 }}
              animate={inView ? { scale: 1 } : { scale: 0.5 }}
              transition={{ delay: 0.7, duration: 0.5, type: "spring" }}
            >
              99%
            </motion.span>
            <span className="text-secondary-700">Satisfaction</span>
          </div>
          
          <div>
            <motion.span 
              className="block text-4xl lg:text-5xl font-bold text-primary-500 mb-2"
              initial={{ scale: 0.5 }}
              animate={inView ? { scale: 1 } : { scale: 0.5 }}
              transition={{ delay: 0.8, duration: 0.5, type: "spring" }}
            >
              24/7
            </motion.span>
            <span className="text-secondary-700">Support</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Benefits