import { useState, useEffect } from 'react'
import Header from './components/layout/Header'
import Hero from './components/sections/Hero'
import FeaturedProducts from './components/sections/FeaturedProducts'
import Benefits from './components/sections/Benefits'
import Testimonials from './components/sections/Testimonials'
import Newsletter from './components/sections/Newsletter'
import Footer from './components/layout/Footer'
import CartSidebar from './components/cart/CartSidebar'
import './App.css'

function App() {
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [cart, setCart] = useState([])
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Simulate loading delay for animation purposes
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 500)
    
    return () => clearTimeout(timer)
  }, [])

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen)
  }

  const addToCart = (product) => {
    const existingItem = cart.find(item => item.id === product.id)
    
    if (existingItem) {
      setCart(cart.map(item => 
        item.id === product.id 
          ? { ...item, quantity: item.quantity + 1 } 
          : item
      ))
    } else {
      setCart([...cart, { ...product, quantity: 1 }])
    }
    
    // Open cart when adding item
    if (!isCartOpen) {
      setIsCartOpen(true)
    }
  }

  const removeFromCart = (productId) => {
    setCart(cart.filter(item => item.id !== productId))
  }

  const updateQuantity = (productId, quantity) => {
    if (quantity < 1) {
      removeFromCart(productId)
      return
    }
    
    setCart(cart.map(item => 
      item.id === productId 
        ? { ...item, quantity } 
        : item
    ))
  }

  return (
    <div className={`app-container ${isLoaded ? 'fade-in' : 'opacity-0'}`}>
      <Header 
        toggleCart={toggleCart} 
        cartItemCount={cart.reduce((total, item) => total + item.quantity, 0)} 
      />
      <main>
        <Hero />
        <FeaturedProducts addToCart={addToCart} />
        <Benefits />
        <Testimonials />
        <Newsletter />
      </main>
      <Footer />
      <CartSidebar 
        isOpen={isCartOpen} 
        toggleCart={toggleCart} 
        cartItems={cart} 
        removeItem={removeFromCart}
        updateQuantity={updateQuantity}
      />
    </div>
  )
}

export default App