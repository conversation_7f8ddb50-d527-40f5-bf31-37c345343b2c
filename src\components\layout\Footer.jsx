import { FiInstagram, FiFacebook, FiTwitter, FiYoutube } from 'react-icons/fi'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  
  return (
    <footer className="bg-secondary-900 text-white pt-16 pb-8">
      <div className="container-custom">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Column */}
          <div className="mb-8 md:mb-0">
            <a href="/" className="inline-flex items-center mb-4">
              <span className="text-3xl mr-1">💪</span>
              <span className="font-display text-xl font-bold">
                MUSCLE<span className="text-primary-400">MAX</span>
              </span>
            </a>
            <p className="text-gray-300 mb-4">
              Premium bodybuilding supplements to help you achieve your fitness goals faster.
            </p>
            <div className="flex space-x-4 text-gray-300">
              <a href="#" className="hover:text-primary-300 transition-colors">
                <FiInstagram className="text-xl" />
              </a>
              <a href="#" className="hover:text-primary-300 transition-colors">
                <FiFacebook className="text-xl" />
              </a>
              <a href="#" className="hover:text-primary-300 transition-colors">
                <FiTwitter className="text-xl" />
              </a>
              <a href="#" className="hover:text-primary-300 transition-colors">
                <FiYoutube className="text-xl" />
              </a>
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><a href="#products" className="text-gray-300 hover:text-primary-300 transition-colors">Products</a></li>
              <li><a href="#benefits" className="text-gray-300 hover:text-primary-300 transition-colors">Benefits</a></li>
              <li><a href="#testimonials" className="text-gray-300 hover:text-primary-300 transition-colors">Testimonials</a></li>
              <li><a href="#contact" className="text-gray-300 hover:text-primary-300 transition-colors">Contact Us</a></li>
            </ul>
          </div>
          
          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-primary-300 transition-colors">Protein Supplements</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary-300 transition-colors">Pre-Workout</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary-300 transition-colors">Weight Gainers</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary-300 transition-colors">BCAAs</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary-300 transition-colors">Fitness Equipment</a></li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-2 text-gray-300">
              <li>123 Fitness Street</li>
              <li>Muscle City, MC 10001</li>
              <li>Phone: +****************</li>
              <li>Email: <EMAIL></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              &copy; {currentYear} MuscleMax. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-gray-300 text-sm">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-gray-300 text-sm">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-gray-300 text-sm">Shipping</a>
              <a href="#" className="text-gray-400 hover:text-gray-300 text-sm">Returns</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer