import { motion } from 'framer-motion'
import { FiShoppingCart, FiStar } from 'react-icons/fi'

const ProductCard = ({ product, addToCart }) => {
  const { name, description, price, originalPrice, rating, reviewCount, image } = product
  const discount = originalPrice ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0
  
  return (
    <motion.div
      className="product-card glass-card overflow-hidden"
      whileHover={{ y: -8 }}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
      }}
    >
      <div className="relative h-[200px] overflow-hidden">
        <img 
          src={image} 
          alt={name} 
          className="w-full h-full object-cover transition-transform duration-700 glow-effect"
        />
        {discount > 0 && (
          <div className="absolute top-3 left-3 bg-primary-500 text-white text-xs font-bold px-2 py-1 rounded">
            {discount}% OFF
          </div>
        )}
      </div>
      
      <div className="p-6">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-lg text-secondary-900 leading-tight">{name}</h3>
          <div className="flex items-center">
            <FiStar className="text-yellow-400 fill-current" />
            <span className="text-secondary-700 ml-1 text-sm">{rating}</span>
          </div>
        </div>
        
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        
        <div className="flex justify-between items-center">
          <div>
            <span className="font-bold text-lg text-secondary-900">${price.toFixed(2)}</span>
            {originalPrice && (
              <span className="text-gray-500 line-through ml-2 text-sm">${originalPrice.toFixed(2)}</span>
            )}
          </div>
          
          <motion.button
            className="flex items-center justify-center w-10 h-10 bg-primary-400 text-white rounded-full"
            whileHover={{ scale: 1.1, backgroundColor: "#68c95f" }}
            whileTap={{ scale: 0.9 }}
            onClick={() => addToCart(product)}
          >
            <FiShoppingCart />
          </motion.button>
        </div>
        
        <div className="mt-4 text-xs text-gray-500">
          {reviewCount} reviews • Free shipping
        </div>
      </div>
    </motion.div>
  )
}

export default ProductCard